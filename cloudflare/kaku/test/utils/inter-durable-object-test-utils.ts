/**
 * Test utilities for inter-durable object communication testing
 * Provides helper functions for setting up and testing communication between
 * ConnectionDO (kaku) and UserDataStoreManager (nico) durable objects
 */

import { SELF } from 'cloudflare:test';
import type { PingMessage, PongMessage, CommunicationResponse } from '../../src/shared/nico-types';

/**
 * Test configuration for inter-durable object communication
 */
export interface InterDurableObjectTestConfig {
  userId: string;
  platform: string;
  connectionName?: string;
}

/**
 * Test setup result containing both durable object stubs
 */
export interface TestSetupResult {
  connectionStub: any; // DurableObjectStub<Connections>
  userDataManagerStub: any; // UserDataStoreManagerStub
  connectionId: any; // DurableObjectId
  userDataManagerId: any; // DurableObjectId
  config: InterDurableObjectTestConfig;
}

/**
 * Utility class for inter-durable object communication testing
 */
export class InterDurableObjectTestUtils {
  /**
   * Set up both durable objects for testing
   */
  static async setupDurableObjects(config: InterDurableObjectTestConfig): Promise<TestSetupResult> {
    const { userId, platform } = config;
    const connectionName = config.connectionName || `${userId}:${platform}`;

    // Get durable object namespaces from the test environment
    const connectionsNamespace = SELF.Connections;
    const userDataManagerNamespace = SELF.USER_DATA_MANAGER_DURABLE_OBJECT;

    // Create durable object IDs
    const connectionId = connectionsNamespace.idFromName(connectionName);
    const userDataManagerId = userDataManagerNamespace.idFromName(userId);

    // Get durable object stubs
    const connectionStub = connectionsNamespace.get(connectionId);
    const userDataManagerStub = userDataManagerNamespace.get(userDataManagerId);

    return {
      connectionStub,
      userDataManagerStub,
      connectionId,
      userDataManagerId,
      config: { ...config, connectionName },
    };
  }

  /**
   * Initialize UserDataStoreManager with the durable object name
   */
  static async initializeUserDataManager(
    userDataManagerStub: any, // UserDataStoreManagerStub
    userId: string,
  ): Promise<void> {
    // Store the durable object name in the UserDataStoreManager
    const response = await userDataManagerStub.fetch(
      new Request('http://localhost/init', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      }),
    );

    if (!response.ok) {
      throw new Error(`Failed to initialize UserDataStoreManager: ${response.status}`);
    }
  }

  /**
   * Test basic ping-pong communication between durable objects
   */
  static async testPingPong(
    connectionStub: any, // DurableObjectStub<Connections>
    testData?: any,
  ): Promise<CommunicationResponse> {
    // Call the ping method on the connection durable object
    const response = await connectionStub.fetch(
      new Request('http://localhost/ping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: testData }),
      }),
    );

    if (!response.ok) {
      throw new Error(`Ping request failed with status: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * Test data serialization and deserialization
   */
  static async testDataSerialization(
    connectionStub: any, // DurableObjectStub<Connections>
    testData: any,
  ): Promise<{ sent: any; received: any; isEqual: boolean }> {
    const response = await this.testPingPong(connectionStub, testData);

    if (!response.success) {
      throw new Error(`Ping-pong failed: ${response.error}`);
    }

    const receivedData = response.data?.data;
    const isEqual = JSON.stringify(testData) === JSON.stringify(receivedData);

    return {
      sent: testData,
      received: receivedData,
      isEqual,
    };
  }

  /**
   * Test communication latency
   */
  static async testCommunicationLatency(
    connectionStub: any, // DurableObjectStub<Connections>
    iterations: number = 5,
  ): Promise<{
    averageLatency: number;
    minLatency: number;
    maxLatency: number;
    latencies: number[];
  }> {
    const latencies: number[] = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      const response = await this.testPingPong(connectionStub, { iteration: i });
      const endTime = Date.now();

      if (!response.success) {
        throw new Error(`Ping-pong failed on iteration ${i}: ${response.error}`);
      }

      const latency = endTime - startTime;
      latencies.push(latency);
    }

    const averageLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
    const minLatency = Math.min(...latencies);
    const maxLatency = Math.max(...latencies);

    return {
      averageLatency,
      minLatency,
      maxLatency,
      latencies,
    };
  }

  /**
   * Clean up test data from both durable objects
   */
  static async cleanup(setup: TestSetupResult): Promise<void> {
    try {
      // Clean up connection durable object
      await setup.connectionStub.fetch(
        new Request('http://localhost/cleanup', {
          method: 'POST',
        }),
      );

      // Clean up user data manager durable object
      await setup.userDataManagerStub.fetch(
        new Request('http://localhost/cleanup', {
          method: 'POST',
        }),
      );
    } catch (error) {
      console.warn('Cleanup failed:', error);
      // Don't throw - cleanup failures shouldn't fail tests
    }
  }

  /**
   * Validate ping message structure
   */
  static validatePingMessage(message: any): message is PingMessage {
    return (
      typeof message === 'object' &&
      message !== null &&
      message.type === 'ping' &&
      typeof message.timestamp === 'number' &&
      typeof message.connectionId === 'string'
    );
  }

  /**
   * Validate pong message structure
   */
  static validatePongMessage(message: any): message is PongMessage {
    return (
      typeof message === 'object' &&
      message !== null &&
      message.type === 'pong' &&
      typeof message.timestamp === 'number' &&
      typeof message.originalTimestamp === 'number' &&
      typeof message.connectionId === 'string'
    );
  }

  /**
   * Validate communication response structure
   */
  static validateCommunicationResponse(response: any): response is CommunicationResponse {
    return (
      typeof response === 'object' &&
      response !== null &&
      typeof response.success === 'boolean' &&
      typeof response.timestamp === 'number'
    );
  }
}

/**
 * Test data generators for various data types
 */
export class TestDataGenerator {
  static simpleString(): string {
    return 'test-string-' + Date.now();
  }

  static simpleObject(): Record<string, any> {
    return {
      id: Date.now(),
      name: 'test-object',
      active: true,
      metadata: {
        created: new Date().toISOString(),
        version: '1.0.0',
      },
    };
  }

  static complexObject(): Record<string, any> {
    return {
      user: {
        id: 'user-' + Date.now(),
        profile: {
          name: 'Test User',
          email: '<EMAIL>',
          preferences: {
            theme: 'dark',
            notifications: true,
            language: 'en',
          },
        },
      },
      session: {
        id: 'session-' + Date.now(),
        startTime: Date.now(),
        activities: [
          { type: 'login', timestamp: Date.now() - 1000 },
          { type: 'navigation', timestamp: Date.now() - 500 },
          { type: 'action', timestamp: Date.now() },
        ],
      },
      metadata: {
        version: '2.0.0',
        environment: 'test',
        features: ['feature1', 'feature2', 'feature3'],
      },
    };
  }

  static arrayData(): any[] {
    return ['string-item', 123, true, { nested: 'object' }, ['nested', 'array'], null];
  }
}
