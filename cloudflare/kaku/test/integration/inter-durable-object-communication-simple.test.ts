/**
 * Integration tests for inter-durable object communication
 * Tests communication between ConnectionDO (kaku) and UserDataStoreManager (nico)
 */

import { describe, it, expect } from 'vitest';
import { env } from 'cloudflare:test';
import type {
  PingMessage,
  PongMessage,
  CommunicationResponse,
  UserDataStoreManagerStub,
} from '../../src/shared/nico-types';

describe('Inter-Durable Object Communication', () => {
  describe('Type System Integration', () => {
    it('should have properly typed UserDataStoreManager binding', () => {
      // Test that the environment has the properly typed binding
      expect(env.USER_DATA_MANAGER_DURABLE_OBJECT).toBeDefined();
      expect(typeof env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName).toBe('function');
      expect(typeof env.USER_DATA_MANAGER_DURABLE_OBJECT.get).toBe('function');
    });

    it('should validate ping message type structure', () => {
      const pingMessage: PingMessage = {
        type: 'ping',
        timestamp: Date.now(),
        connectionId: 'test-connection-123',
        data: { message: 'Hello from ConnectionDO' },
      };

      // Validate required fields
      expect(pingMessage.type).toBe('ping');
      expect(typeof pingMessage.timestamp).toBe('number');
      expect(typeof pingMessage.connectionId).toBe('string');
      expect(pingMessage.data).toBeDefined();
    });

    it('should validate pong message type structure', () => {
      const pongMessage: PongMessage = {
        type: 'pong',
        timestamp: Date.now(),
        originalTimestamp: Date.now() - 1000,
        connectionId: 'test-connection-123',
        data: { message: 'Hello back from UserDataStoreManager' },
      };

      // Validate required fields
      expect(pongMessage.type).toBe('pong');
      expect(typeof pongMessage.timestamp).toBe('number');
      expect(typeof pongMessage.originalTimestamp).toBe('number');
      expect(typeof pongMessage.connectionId).toBe('string');
      expect(pongMessage.data).toBeDefined();
    });

    it('should validate communication response type structure', () => {
      const successResponse: CommunicationResponse = {
        success: true,
        data: {
          type: 'pong',
          timestamp: Date.now(),
          originalTimestamp: Date.now() - 1000,
          connectionId: 'test-connection-123',
          data: { message: 'Test data' },
        },
        timestamp: Date.now(),
      };

      const errorResponse: CommunicationResponse = {
        success: false,
        error: 'Test error message',
        timestamp: Date.now(),
      };

      // Validate success response
      expect(successResponse.success).toBe(true);
      expect(successResponse.data).toBeDefined();
      expect(typeof successResponse.timestamp).toBe('number');

      // Validate error response
      expect(errorResponse.success).toBe(false);
      expect(errorResponse.error).toBeDefined();
      expect(typeof errorResponse.timestamp).toBe('number');
    });

    it('should handle ping without data', async () => {
      const userId = 'test-user-no-data-' + Date.now();
      const platform = 'test-platform';
      const connectionName = `${userId}:${platform}`;

      const connectionsNamespace = SELF.Connections;
      const userDataManagerNamespace = SELF.USER_DATA_MANAGER_DURABLE_OBJECT;

      const connectionId = connectionsNamespace.idFromName(connectionName);
      const userDataManagerId = userDataManagerNamespace.idFromName(userId);

      const connectionStub = connectionsNamespace.get(connectionId);
      const userDataManagerStub = userDataManagerNamespace.get(userDataManagerId);

      // Initialize the UserDataStoreManager
      const initResponse = await userDataManagerStub.fetch(
        new Request('http://localhost/init', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userId }),
        }),
      );
      expect(initResponse.ok).toBe(true);

      // Test ping without data
      const pingResponse = await connectionStub.fetch(
        new Request('http://localhost/ping', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}),
        }),
      );

      expect(pingResponse.ok).toBe(true);
      const result = await pingResponse.json();

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.type).toBe('pong');
      expect(result.data.data).toBeUndefined();
    });
  });

  describe('Data Serialization and Deserialization', () => {
    it('should correctly serialize and deserialize complex object data', async () => {
      const userId = 'test-user-complex-' + Date.now();
      const platform = 'test-platform';
      const connectionName = `${userId}:${platform}`;

      const connectionsNamespace = SELF.Connections;
      const userDataManagerNamespace = SELF.USER_DATA_MANAGER_DURABLE_OBJECT;

      const connectionId = connectionsNamespace.idFromName(connectionName);
      const userDataManagerId = userDataManagerNamespace.idFromName(userId);

      const connectionStub = connectionsNamespace.get(connectionId);
      const userDataManagerStub = userDataManagerNamespace.get(userDataManagerId);

      // Initialize the UserDataStoreManager
      const initResponse = await userDataManagerStub.fetch(
        new Request('http://localhost/init', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userId }),
        }),
      );
      expect(initResponse.ok).toBe(true);

      // Test complex data serialization
      const testData = {
        user: {
          id: 'user-' + Date.now(),
          profile: {
            name: 'Test User',
            email: '<EMAIL>',
            preferences: {
              theme: 'dark',
              notifications: true,
              language: 'en',
            },
          },
        },
        session: {
          id: 'session-' + Date.now(),
          startTime: Date.now(),
          activities: [
            { type: 'login', timestamp: Date.now() - 1000 },
            { type: 'navigation', timestamp: Date.now() - 500 },
            { type: 'action', timestamp: Date.now() },
          ],
        },
      };

      const pingResponse = await connectionStub.fetch(
        new Request('http://localhost/ping', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ data: testData }),
        }),
      );

      expect(pingResponse.ok).toBe(true);
      const result = await pingResponse.json();

      expect(result.success).toBe(true);
      expect(result.data.data).toEqual(testData);
    });
  });

  describe('Communication Performance', () => {
    it('should complete ping-pong communication within reasonable time', async () => {
      const userId = 'test-user-perf-' + Date.now();
      const platform = 'test-platform';
      const connectionName = `${userId}:${platform}`;

      const connectionsNamespace = SELF.Connections;
      const userDataManagerNamespace = SELF.USER_DATA_MANAGER_DURABLE_OBJECT;

      const connectionId = connectionsNamespace.idFromName(connectionName);
      const userDataManagerId = userDataManagerNamespace.idFromName(userId);

      const connectionStub = connectionsNamespace.get(connectionId);
      const userDataManagerStub = userDataManagerNamespace.get(userDataManagerId);

      // Initialize the UserDataStoreManager
      const initResponse = await userDataManagerStub.fetch(
        new Request('http://localhost/init', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userId }),
        }),
      );
      expect(initResponse.ok).toBe(true);

      // Test performance
      const startTime = Date.now();
      const pingResponse = await connectionStub.fetch(
        new Request('http://localhost/ping', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ data: { performance: 'test' } }),
        }),
      );
      const endTime = Date.now();

      expect(pingResponse.ok).toBe(true);
      const result = await pingResponse.json();

      expect(result.success).toBe(true);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });
  });

  describe('Message Validation', () => {
    it('should validate ping and pong message structures', () => {
      // Test ping message validation
      const validPingMessage = {
        type: 'ping',
        timestamp: Date.now(),
        connectionId: 'test-connection',
        data: { test: 'data' },
      };

      const invalidPingMessage = {
        type: 'invalid',
        timestamp: Date.now(),
        connectionId: 'test-connection',
      };

      // Basic validation logic (simplified)
      const validatePingMessage = (msg: any) => {
        return (
          msg &&
          msg.type === 'ping' &&
          typeof msg.timestamp === 'number' &&
          typeof msg.connectionId === 'string'
        );
      };

      expect(validatePingMessage(validPingMessage)).toBe(true);
      expect(validatePingMessage(invalidPingMessage)).toBe(false);
      expect(validatePingMessage(null)).toBe(false);
      expect(validatePingMessage(undefined)).toBe(false);

      // Test pong message validation
      const validPongMessage = {
        type: 'pong',
        timestamp: Date.now(),
        originalTimestamp: Date.now() - 1000,
        connectionId: 'test-connection',
        data: { test: 'data' },
      };

      const invalidPongMessage = {
        type: 'pong',
        timestamp: Date.now(),
        // missing originalTimestamp
        connectionId: 'test-connection',
      };

      const validatePongMessage = (msg: any) => {
        return (
          msg &&
          msg.type === 'pong' &&
          typeof msg.timestamp === 'number' &&
          typeof msg.originalTimestamp === 'number' &&
          typeof msg.connectionId === 'string'
        );
      };

      expect(validatePongMessage(validPongMessage)).toBe(true);
      expect(validatePongMessage(invalidPongMessage)).toBe(false);
    });
  });
});
