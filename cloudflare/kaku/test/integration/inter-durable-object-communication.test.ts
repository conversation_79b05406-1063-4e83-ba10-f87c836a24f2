/**
 * Integration tests for inter-durable object communication
 * Tests communication between ConnectionDO (kaku) and UserDataStoreManager (nico)
 */

import { describe, it, expect } from 'vitest';
import { SELF } from 'cloudflare:test';

describe('Inter-Durable Object Communication', () => {
  describe('Basic Ping-Pong Communication', () => {
    it('should successfully send ping and receive pong', async () => {
      const response = await InterDurableObjectTestUtils.testPingPong(testSetup.connectionStub, {
        message: 'Hello from ConnectionDO',
      });

      expect(response.success).toBe(true);
      expect(response.data).toBeDefined();
      expect(response.data.type).toBe('pong');
      expect(response.data.connectionId).toBe(testSetup.config.connectionName);
      expect(response.data.data).toEqual({ message: 'Hello from ConnectionDO' });
      expect(typeof response.data.timestamp).toBe('number');
      expect(typeof response.data.originalTimestamp).toBe('number');
    });

    it('should handle ping without data', async () => {
      const response = await InterDurableObjectTestUtils.testPingPong(testSetup.connectionStub);

      expect(response.success).toBe(true);
      expect(response.data).toBeDefined();
      expect(response.data.type).toBe('pong');
      expect(response.data.data).toBeUndefined();
    });

    it('should include durable object name in pong response', async () => {
      const response = await InterDurableObjectTestUtils.testPingPong(testSetup.connectionStub, {
        test: 'data',
      });

      expect(response.success).toBe(true);
      expect(response.data.durableObjectName).toBe(testSetup.config.userId);
    });
  });

  describe('Data Serialization and Deserialization', () => {
    it('should correctly serialize and deserialize simple string data', async () => {
      const testData = TestDataGenerator.simpleString();
      const result = await InterDurableObjectTestUtils.testDataSerialization(
        testSetup.connectionStub,
        testData,
      );

      expect(result.isEqual).toBe(true);
      expect(result.sent).toBe(testData);
      expect(result.received).toBe(testData);
    });

    it('should correctly serialize and deserialize simple object data', async () => {
      const testData = TestDataGenerator.simpleObject();
      const result = await InterDurableObjectTestUtils.testDataSerialization(
        testSetup.connectionStub,
        testData,
      );

      expect(result.isEqual).toBe(true);
      expect(result.sent).toEqual(testData);
      expect(result.received).toEqual(testData);
    });

    it('should correctly serialize and deserialize complex object data', async () => {
      const testData = TestDataGenerator.complexObject();
      const result = await InterDurableObjectTestUtils.testDataSerialization(
        testSetup.connectionStub,
        testData,
      );

      expect(result.isEqual).toBe(true);
      expect(result.sent).toEqual(testData);
      expect(result.received).toEqual(testData);
    });

    it('should correctly serialize and deserialize array data', async () => {
      const testData = TestDataGenerator.arrayData();
      const result = await InterDurableObjectTestUtils.testDataSerialization(
        testSetup.connectionStub,
        testData,
      );

      expect(result.isEqual).toBe(true);
      expect(result.sent).toEqual(testData);
      expect(result.received).toEqual(testData);
    });

    it('should handle null and undefined values', async () => {
      const testData = {
        nullValue: null,
        undefinedValue: undefined,
        emptyString: '',
        emptyObject: {},
        emptyArray: [],
      };

      const result = await InterDurableObjectTestUtils.testDataSerialization(
        testSetup.connectionStub,
        testData,
      );

      // Note: undefined values are lost during JSON serialization
      expect(result.received.nullValue).toBe(null);
      expect(result.received.undefinedValue).toBeUndefined();
      expect(result.received.emptyString).toBe('');
      expect(result.received.emptyObject).toEqual({});
      expect(result.received.emptyArray).toEqual([]);
    });
  });

  describe('Communication Performance', () => {
    it('should complete ping-pong communication within reasonable time', async () => {
      const startTime = Date.now();
      const response = await InterDurableObjectTestUtils.testPingPong(testSetup.connectionStub, {
        performance: 'test',
      });
      const endTime = Date.now();

      expect(response.success).toBe(true);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should measure communication latency', async () => {
      const latencyResults = await InterDurableObjectTestUtils.testCommunicationLatency(
        testSetup.connectionStub,
        3, // 3 iterations for faster test
      );

      expect(latencyResults.latencies).toHaveLength(3);
      expect(latencyResults.averageLatency).toBeGreaterThan(0);
      expect(latencyResults.minLatency).toBeGreaterThan(0);
      expect(latencyResults.maxLatency).toBeGreaterThan(0);
      expect(latencyResults.minLatency).toBeLessThanOrEqual(latencyResults.averageLatency);
      expect(latencyResults.averageLatency).toBeLessThanOrEqual(latencyResults.maxLatency);
    });
  });

  describe('Message Validation', () => {
    it('should validate ping message structure', () => {
      const validPingMessage = {
        type: 'ping',
        timestamp: Date.now(),
        connectionId: 'test-connection',
        data: { test: 'data' },
      };

      const invalidPingMessage = {
        type: 'invalid',
        timestamp: Date.now(),
        connectionId: 'test-connection',
      };

      expect(InterDurableObjectTestUtils.validatePingMessage(validPingMessage)).toBe(true);
      expect(InterDurableObjectTestUtils.validatePingMessage(invalidPingMessage)).toBe(false);
      expect(InterDurableObjectTestUtils.validatePingMessage(null)).toBe(false);
      expect(InterDurableObjectTestUtils.validatePingMessage(undefined)).toBe(false);
    });

    it('should validate pong message structure', () => {
      const validPongMessage = {
        type: 'pong',
        timestamp: Date.now(),
        originalTimestamp: Date.now() - 1000,
        connectionId: 'test-connection',
        data: { test: 'data' },
      };

      const invalidPongMessage = {
        type: 'pong',
        timestamp: Date.now(),
        // missing originalTimestamp
        connectionId: 'test-connection',
      };

      expect(InterDurableObjectTestUtils.validatePongMessage(validPongMessage)).toBe(true);
      expect(InterDurableObjectTestUtils.validatePongMessage(invalidPongMessage)).toBe(false);
    });

    it('should validate communication response structure', () => {
      const validResponse = {
        success: true,
        data: { test: 'data' },
        timestamp: Date.now(),
      };

      const invalidResponse = {
        success: 'true', // should be boolean
        data: { test: 'data' },
        timestamp: Date.now(),
      };

      expect(InterDurableObjectTestUtils.validateCommunicationResponse(validResponse)).toBe(true);
      expect(InterDurableObjectTestUtils.validateCommunicationResponse(invalidResponse)).toBe(
        false,
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle communication errors gracefully', async () => {
      // Test with invalid connection setup
      const invalidSetup = await InterDurableObjectTestUtils.setupDurableObjects({
        userId: 'invalid-user',
        platform: 'invalid-platform',
      });

      // Don't initialize the UserDataStoreManager to simulate error
      const response = await InterDurableObjectTestUtils.testPingPong(invalidSetup.connectionStub, {
        test: 'error-handling',
      });

      // The ping should still work, but might have different behavior
      // depending on how the UserDataStoreManager handles uninitialized state
      expect(typeof response.success).toBe('boolean');
      expect(typeof response.timestamp).toBe('number');
    });
  });

  describe('Multiple Concurrent Communications', () => {
    it('should handle multiple concurrent ping-pong communications', async () => {
      const promises = Array.from({ length: 5 }, (_, i) =>
        InterDurableObjectTestUtils.testPingPong(testSetup.connectionStub, {
          iteration: i,
          concurrent: true,
        }),
      );

      const results = await Promise.all(promises);

      results.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.data.data.iteration).toBe(index);
        expect(result.data.data.concurrent).toBe(true);
      });
    });
  });
});
