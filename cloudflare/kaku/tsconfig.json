{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "skipLibCheck": true, "lib": ["ESNext", "DOM", "DOM.Iterable"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "types": ["vite/client"], "paths": {"@nico/*": ["../nico/src/*"]}}, "include": ["src/**/*", "vite.config.ts", "worker-configuration.d.ts"], "exclude": ["node_modules", "dist", "public/out"]}