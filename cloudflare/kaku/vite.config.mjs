import { defineConfig } from 'vite';
import { cloudflare } from '@cloudflare/vite-plugin';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  plugins: [
    cloudflare({
      configPath: './wrangler.jsonc',
      auxiliaryWorkers: [
        {
          configPath: '../nico/wrangler.toml',
        },
      ],
    }),
    tailwindcss(),
  ],

  publicDir: 'public',

  build: {
    outDir: 'dist',
    minify: process.env.NODE_ENV === 'production',
    sourcemap: true,
  },

  server: {
    port: 8787,
    host: true,
  },

  esbuild: {
    jsx: 'automatic',
    jsxImportSource: 'hono/jsx',
  },
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV !== 'production'),
  },
});
