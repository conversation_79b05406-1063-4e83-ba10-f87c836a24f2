/**
 * Shared types module for importing UserDataStoreManager types from the nico project
 * This ensures type consistency across both kaku and nico projects when working with UserDataStoreManager
 */

// Import the UserDataStoreManager class and related types from nico project
export { UserDataStoreManager } from '@nico/durable-object/UserDataStoreManager';

// Import and re-export all types from nico project
export type {
  Environment as NicoEnvironment,
  RPCResponse,
  RPCFailureResponse,
  User as NicoUser,
  NicoA<PERSON>,
  EmailActivityMessage,
  
  // Group context types
  GroupContextType,
  GroupContextCreateType,
  GroupContextPatchType,
  AccumulatedAssignmentGroupDataType,
  AccumulatedAssignmentGroupDataStatsType,
  
  // Assignment types
  AssignmentCreateType,
  
  // Reward types
  RewardCreateType,
  RewardResponseType,
  
  // Scheduled operations types
  ScheduledOperation,
  ScheduledOperationCreateType,
  ScheduledOperationStatus,
  
  // Subscription types
  Subscription,
  SubscriptionCreate,
  SubscriptionHistory,
  SubscriptionHistoryCreate,
} from '@nico/types';

// Import withdrawal types
export type {
  WithdrawalLimit,
  PayoutLimits,
  AllowedWithdrawalCount,
} from '@nico/types/withdrawals';

/**
 * Type for UserDataStoreManager durable object stub with proper typing
 * This provides the correctly typed interface for communicating with UserDataStoreManager
 */
export type UserDataStoreManagerStub = DurableObjectStub<UserDataStoreManager>;

/**
 * Communication message types for inter-durable object communication
 */
export interface PingMessage {
  type: 'ping';
  timestamp: number;
  connectionId: string;
  data?: any;
}

export interface PongMessage {
  type: 'pong';
  timestamp: number;
  originalTimestamp: number;
  connectionId: string;
  data?: any;
}

export type InterDurableObjectMessage = PingMessage | PongMessage;

/**
 * Response wrapper for inter-durable object communication
 */
export interface CommunicationResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}
