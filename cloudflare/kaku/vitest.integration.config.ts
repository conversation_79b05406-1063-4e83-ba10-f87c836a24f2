import { defineWorkersConfig } from '@cloudflare/vitest-pool-workers/config';

export default defineWorkersConfig(async () => {
  return {
    test: {
      include: ['test/integration/**/*.{test,spec}.{js,mjs,cjs,ts,tsx,jsx}'],
      setupFiles: ['./vitest.setup.ts'],
      testTimeout: 30000,
      globals: true,
      poolOptions: {
        workers: {
          singleWorker: true,
          isolatedStorage: false,
          miniflare: {
            compatibilityDate: '2024-09-23',
            compatibilityFlags: ['nodejs_compat_v2'],
            main: 'src/api/index.ts',
            durable_objects: {
              bindings: [
                { name: 'Connections', class_name: 'Connections' },
                {
                  name: 'USER_DATA_MANAGER_DURABLE_OBJECT',
                  class_name: 'UserDataStoreManager',
                },
              ],
              migrations: [
                {
                  tag: 'v1',
                  new_sqlite_classes: ['Connections'],
                },
                {
                  tag: 'v2',
                  new_sqlite_classes: ['UserDataStoreManager'],
                },
              ],
            },
            r2_buckets: [
              {
                binding: 'SCREENSHOTS_INBOUND_BUCKET',
                bucket_name: 'test-screenshots-bucket',
              },
            ],
          },
          wrangler: { configPath: './wrangler.jsonc' },
        },
      },
    },
  };
});
